import { expect, use } from "chai";
import * as sinon from "sinon";
import { getStaticDomainPoolService } from "../../skywind/services/staticDomainPool";
import {
    DomainPoolAlreadyExistsError,
    DomainPoolNotFoundError,
    DomainPoolItemAlreadyExistsError,
    DomainPoolItemNotFoundError
} from "../../skywind/errors";
import { StaticDomainPoolService, DomainPoolCreateData } from "../../skywind/entities/domainPool";
import { Models } from "../../skywind/models/models";

use(require("chai-as-promised"));

const StaticDomainModel = Models.StaticDomainModel;
const StaticDomainPoolModel = Models.StaticDomainPoolModel;
const StaticDomainPoolItemModel = Models.StaticDomainPoolItemModel;

describe("StaticDomainPoolService", () => {
    let service: StaticDomainPoolService;
    let sandbox: sinon.SinonSandbox;

    beforeEach(() => {
        service = getStaticDomainPoolService();
        sandbox = sinon.createSandbox();
    });

    afterEach(() => {
        sandbox.restore();
    });

    describe("create", () => {
        it("should create a new domain pool successfully", async () => {
            const poolData: DomainPoolCreateData = {
                name: "Test Pool",
                domains: [
                    { id: 1 },
                    { id: 2 }
                ]
            };

            sandbox.stub(StaticDomainModel, "findAll").resolves([{ get: () => 1 }, { get: () => 2 }] as any);
            sandbox.stub(StaticDomainPoolModel, "findOne").resolves(null);
            sandbox.stub(StaticDomainPoolModel, "create").resolves({
                get: () => 1
            } as any);
            sandbox.stub(StaticDomainPoolItemModel, "bulkCreate").resolves();
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 1,
                    name: "Test Pool",
                    domains: [
                        { id: 1, isActive: true },
                        { id: 2, isActive: false }
                    ]
                })
            } as any);

            const result = await service.create(poolData);

            expect(result).to.deep.equal({
                id: 1,
                name: "Test Pool",
                domains: [
                    { id: 1, isActive: true },
                    { id: 2, isActive: false }
                ]
            });
        });

        it("should create a domain pool with domainDetectorAdapterId", async () => {
            const poolData: DomainPoolCreateData = {
                name: "Test Pool with Adapter",
                domainDetectorAdapterId: "tapking",
                domains: []
            };

            sandbox.stub(StaticDomainPoolModel, "findOne").resolves(null);
            sandbox.stub(StaticDomainPoolModel, "create").resolves({
                get: () => 1
            } as any);
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 1,
                    name: "Test Pool with Adapter",
                    domainDetectorAdapterId: "tapking",
                    domains: []
                })
            } as any);

            const result = await service.create(poolData);

            expect(result).to.deep.equal({
                id: 1,
                name: "Test Pool with Adapter",
                domainDetectorAdapterId: "tapking",
                domains: []
            });
        });

        it("should throw DomainPoolAlreadyExistsError if pool name already exists", async () => {
            sandbox.stub(StaticDomainPoolModel, "findOne").resolves({ id: 1 } as any);

            await expect(service.create({ name: "Existing Pool", domains: [] }))
                .to.eventually.be.rejectedWith(DomainPoolAlreadyExistsError);
        });
    });

    describe("update", () => {
        it("should update pool name successfully", async () => {
            const poolId = 1;

            sandbox.stub(StaticDomainPoolModel, "findByPk")
                .onFirstCall().resolves({ update: sandbox.stub().resolves(), get: () => poolId } as any)
                .onSecondCall().resolves({
                toJSON: () => ({
                    id: poolId,
                    name: "Updated Pool",
                    domains: []
                })
            } as any);

            const result = await service.update(poolId, { name: "Updated Pool" });

            expect(result).to.deep.equal({
                id: poolId,
                name: "Updated Pool",
                domains: []
            });
        });

        it("should update pool domains successfully", async () => {
            const poolId = 1;
            const updateData: Partial<DomainPoolCreateData> = {
                domains: [
                    { id: 3 },
                    { id: 4 }
                ]
            };

            sandbox.stub(StaticDomainModel, "findAll").resolves([{ get: () => 3 }, { get: () => 4 }] as any);
            sandbox.stub(StaticDomainPoolModel, "findByPk")
                .onFirstCall().resolves({ update: sandbox.stub().resolves(), get: () => poolId } as any)
                .onSecondCall().resolves({
                toJSON: () => ({
                    id: poolId,
                    name: "Test Pool",
                    domains: [
                        { id: 3, isActive: true },
                        { id: 4, isActive: false }
                    ]
                })
            } as any);

            sandbox.stub(StaticDomainPoolItemModel, "destroy").resolves();
            sandbox.stub(StaticDomainPoolItemModel, "bulkCreate").resolves();

            const result = await service.update(poolId, updateData);

            expect(result).to.deep.equal({
                id: poolId,
                name: "Test Pool",
                domains: [
                    { id: 3, isActive: true },
                    { id: 4, isActive: false }
                ]
            });
        });

        it("should throw DomainPoolNotFoundError if pool does not exist", async () => {
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves(null);

            await expect(service.update(999, { name: "Non-existent Pool" }))
                .to.eventually.be.rejectedWith(DomainPoolNotFoundError);
        });
    });

    describe("findById", () => {
        it("should return pool data when found", async () => {
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({
                toJSON: () => ({
                    id: 1,
                    name: "Existing Pool",
                    domains: [
                        { id: 1, isActive: true },
                        { id: 2, isActive: false }
                    ]
                })
            } as any);

            const result = await service.findById(1);

            expect(result).to.deep.equal({
                id: 1,
                name: "Existing Pool",
                domains: [
                    { id: 1, isActive: true },
                    { id: 2, isActive: false }
                ]
            });
        });

        it("should throw DomainPoolNotFoundError if pool does not exist", async () => {
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves(null);

            await expect(service.findById(999)).to.eventually.be.rejectedWith(DomainPoolNotFoundError);
        });
    });

    describe("findAll", () => {
        it("should return all domain pools", async () => {
            const pools = [
                {
                    id: 1,
                    name: "Pool One",
                    domains: [{ id: 1, isActive: true }]
                },
                {
                    id: 2,
                    name: "Pool Two",
                    domains: [{ id: 2, isActive: false }]
                }
            ];

            sandbox.stub(StaticDomainPoolModel, "findAll").resolves(
                pools.map(pool => ({ toJSON: () => pool })) as any
            );

            const result = await service.findAll();

            expect(result).to.deep.equal(pools);
        });

        it("should apply find options if provided", async () => {
            const findOptions = { where: { name: "Pool One" } };
            const pool = { id: 1, name: "Pool One", domains: [] };

            sandbox.stub(StaticDomainPoolModel, "findAll").resolves([{
                toJSON: () => pool
            }] as any);

            const result = await service.findAll(findOptions);

            expect(result).to.deep.equal([pool]);
        });
    });

    describe("remove", () => {
        it("should remove pool successfully", async () => {
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves({ id: 1 } as any);
            sandbox.stub(StaticDomainPoolModel, "destroy").resolves();

            await service.remove(1);

            expect((StaticDomainPoolModel.destroy as any).calledOnceWith({ where: { id: 1 } })).to.be.true;
        });

        it("should throw DomainPoolNotFoundError if pool does not exist", async () => {
            sandbox.stub(StaticDomainPoolModel, "findByPk").resolves(null);

            await expect(service.remove(999)).to.eventually.be.rejectedWith(DomainPoolNotFoundError);
        });
    });

    describe("addDomain", () => {
        it("should add a domain to the pool successfully", async () => {
            sandbox.stub(StaticDomainModel, "findByPk").resolves({} as any);
            sandbox.stub(StaticDomainPoolItemModel, "findOne").resolves(null);
            sandbox.stub(StaticDomainPoolItemModel, "create").resolves({
                toJSON: () => ({
                    id: 1,
                    staticDomainPoolId: 1,
                    staticDomainId: 2,
                    isActive: true
                })
            }  as any);

            const result = await service.addDomain(1, 2);

            expect(result).to.deep.equal({
                id: 1,
                staticDomainPoolId: 1,
                staticDomainId: 2,
                isActive: true
            });
        });

        it("should throw DomainPoolItemAlreadyExistsError if association exists", async () => {
            sandbox.stub(StaticDomainModel, "findByPk").resolves({} as any);
            sandbox.stub(StaticDomainPoolItemModel, "findOne").resolves({ id: 1 } as any);

            await expect(service.addDomain(1, 2)).to.eventually.be.rejectedWith(DomainPoolItemAlreadyExistsError);
        });
    });

    describe("removeDomain", () => {
        it("should remove a domain from the pool successfully", async () => {
            sandbox.stub(StaticDomainPoolItemModel, "findOne").resolves({ id: 1 } as any);
            sandbox.stub(StaticDomainPoolItemModel, "destroy").resolves();

            await service.removeDomain(1, 2);

            expect((StaticDomainPoolItemModel.destroy as any).calledOnceWith({
                where: { staticDomainId: 2, staticDomainPoolId: 1 }
            })).to.be.true;
        });

        it("should throw DomainPoolItemNotFoundError if association does not exist", async () => {
            sandbox.stub(StaticDomainPoolItemModel, "findOne").resolves(null);

            await expect(service.removeDomain(1, 2)).to.eventually.be.rejectedWith(DomainPoolItemNotFoundError);
        });
    });

    describe("enableDomain", () => {
        it("should enable a domain in the pool successfully", async () => {
            const updatePoolItemStatusStub = sandbox.stub(service as any, "updatePoolItemStatus").resolves();

            await service.enableDomain(1, 2);

            expect(updatePoolItemStatusStub.calledOnceWith(1, 2, true)).to.be.true;
        });

        it("should throw DomainPoolItemNotFoundError if association does not exist", async () => {
            sandbox.stub(StaticDomainModel, "findByPk").resolves({} as any);
            sandbox.stub(StaticDomainPoolItemModel, "findOne").resolves(null);

            await expect(service.enableDomain(1, 2)).to.eventually.be.rejectedWith(DomainPoolItemNotFoundError);
        });
    });

    describe("disableDomain", () => {
        it("should disable a domain in the pool successfully", async () => {
            const updatePoolItemStatusStub = sandbox.stub(service as any, "updatePoolItemStatus").resolves();

            await service.disableDomain(1, 2);

            expect(updatePoolItemStatusStub.calledOnceWith(1, 2, false)).to.be.true;
        });

        it("should throw DomainPoolItemNotFoundError if association does not exist", async () => {
            sandbox.stub(StaticDomainModel, "findByPk").resolves({} as any);
            sandbox.stub(StaticDomainPoolItemModel, "findOne").resolves(null);

            await expect(service.disableDomain(1, 2)).to.eventually.be.rejectedWith(DomainPoolItemNotFoundError);
        });
    });
});
